<!DOCTYPE html>
<html lang="az">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI ilə blok-sxem alqoritmləri</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/svg-pan-zoom/dist/svg-pan-zoom.min.js"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        --background: #000000;
        --surface-1: #111111;
        --surface-2: #1f1f1f;
        --surface-3: #2c2c2c;
        --text-primary: #ffffff;
        --text-secondary: #a0a0a0;
        --border-color: #3a3a3a;
        --accent-color: #ffffff;
        --accent-text: #000000;
      }
      body {
        font-family: "Inter", sans-serif;
        background-color: var(--background);
        color: var(--text-primary);
      }
      ::-webkit-scrollbar {
        width: 8px;
      }
      ::-webkit-scrollbar-track {
        background: var(--surface-1);
      }
      ::-webkit-scrollbar-thumb {
        background-color: var(--surface-3);
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background-color: #555;
      }
      .loader {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #333;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Pan-zoom cursor styles */
      #output.panning {
        cursor: grabbing !important;
      }

      #output svg {
        max-width: none !important;
        max-height: none !important;
        width: 100% !important;
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        object-fit: fill !important;
      }

      #output {
        position: relative !important;
      }

      #output #placeholder-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
      }

      #output .node {
        cursor: pointer;
      }
      #output .node:hover rect,
      #output .node:hover polygon,
      #output .node:hover ellipse {
        stroke-width: 2px !important;
        stroke: var(--accent-color) !important;
      }
      .chat-message {
        max-width: 85%;
        padding: 0.75rem 1rem;
        border-radius: 0.75rem;
      }
      .user-message {
        background-color: var(--accent-color);
        color: var(--accent-text);
        align-self: flex-end;
        border-bottom-right-radius: 0.25rem;
      }
      .assistant-message {
        background-color: var(--surface-2);
        align-self: flex-start;
        border-bottom-left-radius: 0.25rem;
      }
      .assistant-message p,
      .assistant-message ul,
      .assistant-message ol {
        margin-bottom: 0.5rem;
      }
      .assistant-message ul,
      .assistant-message ol {
        padding-left: 1.5rem;
      }
      .assistant-message li {
        margin-bottom: 0.25rem;
      }
      .assistant-message strong {
        color: #fff;
      }

      code {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 0.1em 0.3em;
        border-radius: 4px;
        font-family: "Courier New", Courier, monospace;
        font-size: 0.9em;
      }
      pre code {
        background-color: #111 !important;
        display: block;
        padding: 1rem;
        border-radius: 0.5rem;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      .modal-content code {
        background-color: #111;
        display: block;
        padding: 1rem;
        border-radius: 0.5rem;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      #output svg {
        width: 100%;
        height: auto;
        max-height: 100%;
      }
    </style>
  </head>
  <body class="bg-[--background] text-[--text-primary]">
    <div class="flex h-screen">
      <!-- History Panel -->
      <aside
        id="history-panel"
        class="w-64 flex-shrink-0 bg-[--surface-1] p-4 flex flex-col border-r border-[--border-color]"
      >
        <h1 class="text-xl font-bold mb-4">Blok-sxem.app</h1>
        <button
          id="new-chat-btn"
          class="w-full mb-4 bg-[--accent-color] text-[--accent-text] font-bold py-2 px-4 rounded-lg hover:opacity-90 transition-opacity"
        >
          + Yeni Söhbət
        </button>
        <div class="flex-grow overflow-y-auto pr-2">
          <h2 class="text-sm font-semibold text-[--text-secondary] mb-2">
            Tarixçə
          </h2>
          <ul id="history-list" class="space-y-2">
            <!-- History items will be injected here -->
          </ul>
        </div>
        <footer class="text-center mt-4 text-[--text-secondary] text-xs">
          <p>Gemini & Mermaid.js ilə hazırlanmışdır.</p>
        </footer>
      </aside>

      <!-- Main Content -->
      <main class=" flex flex-col">
        <!-- Top Section: Input and Chat -->
        <div class="flex p-6 gap-6 h-80">
          <!-- Middle: Input Section -->
          <div class="w-1/2">
            <div
              class="bg-[--surface-1] border border-[--border-color] rounded-xl p-6 shadow-lg h-full flex flex-col"
            >
              <label
                for="userInput"
                class="block text-lg font-semibold text-[--text-primary] mb-3"
                >1. Alqoritmin təsviri</label
              >
              <textarea
                id="userInput"
                rows="4"
                class="w-full p-4 bg-[--surface-2] border border-[--border-color] rounded-lg focus:ring-2 focus:ring-[--accent-color] focus:outline-none placeholder:text-[--text-secondary] resize-none flex-grow"
                placeholder="Məsələn: İstifadəçidən alınan ədədin cüt və ya tək olduğunu yoxlayıb nəticəni MySQL verilənlər bazasına yazan proqramın alqoritmi..."
              ></textarea>

              <div class="mt-4 flex gap-4">
                <div class="flex-grow">
                  <label
                    for="lang-select"
                    class="block text-sm font-medium text-[--text-secondary] mb-2"
                    >Proqramlaşdırma dilini seçin</label
                  >
                  <select
                    id="lang-select"
                    class="w-full p-3 bg-[--surface-2] border border-[--border-color] rounded-lg focus:ring-2 focus:ring-[--accent-color] focus:outline-none"
                  >
                    <option value="C#">C#</option>
                    <option value="C++">C++</option>
                    <option value="CSS">CSS</option>
                    <option value="Go">Go</option>
                    <option value="HTML">HTML</option>
                    <option value="Java">Java</option>
                    <option selected value="JavaScript">JavaScript</option>
                    <option value="JSON">JSON</option>
                    <option value="Kotlin">Kotlin</option>
                    <option value="PHP">PHP</option>
                    <option value="Python">Python</option>
                    <option value="Ruby">Ruby</option>
                    <option value="Rust">Rust</option>
                    <option value="SQL">SQL</option>
                    <option value="Swift">Swift</option>
                    <option value="TypeScript">TypeScript</option>
                    <option value="XML">XML</option>
                    <option value="YAML">YAML</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button
                    id="generateBtn"
                    class="bg-[--accent-color] text-[--accent-text] font-bold py-3 px-8 rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center whitespace-nowrap"
                  >
                    <span id="btn-text">Generasiya et</span>
                    <div
                      id="loader"
                      class="loader hidden ml-3"
                      style="width: 20px; height: 20px; border-top-color: black"
                    ></div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Right: Chat Section -->
          <div id="chat-section" class="w-1/2 hidden">
            <div
              class="bg-[--surface-1] border border-[--border-color] rounded-xl shadow-lg h-full flex flex-col"
            >
              <div class="p-4 border-b border-[--border-color]">
                <h2 class="text-lg font-semibold">
                  2. Alqoritmi müzakirə edin
                </h2>
              </div>
              <div
                id="chat-messages"
                class="flex-grow p-4 space-y-4 overflow-y-auto flex flex-col"
              ></div>
              <div
                class="p-4 border-t border-[--border-color] flex items-center gap-4"
              >
                <input
                  type="text"
                  id="chatInput"
                  class="flex-grow p-3 bg-[--surface-2] border border-[--border-color] rounded-lg focus:ring-2 focus:ring-[--accent-color] focus:outline-none placeholder:text-[--text-secondary]"
                  placeholder="Bir sual verin..."
                />
                <button
                  id="sendBtn"
                  class="bg-[--accent-color] text-[--accent-text] font-semibold py-3 px-5 rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center"
                >
                  <span id="send-btn-text">Göndər</span>
                  <div id="chat-loader" class="loader hidden ml-2"></div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Bottom Section: Canvas -->
        <div class="flex-grow p-6 pt-0">
          <div
            class="bg-[--surface-1] h-1/2 border border-[--border-color] rounded-xl shadow-lg flex flex-col overflow-hidden"
          >
            <div
              class="p-4 flex justify-between items-center border-b border-[--border-color]"
            >
              <h2 class="text-lg font-semibold">Blok-sxem</h2>
              <div id="canvas-controls" class="flex items-center gap-2 hidden">
                <button
                  id="zoom-in"
                  class="p-2 rounded-md hover:bg-[--surface-2] transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
                <button
                  id="zoom-out"
                  class="p-2 rounded-md hover:bg-[--surface-2] transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
                <button
                  id="reset-pan"
                  class="p-2 rounded-md hover:bg-[--surface-2] transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M3 2v6h6"></path>
                    <path d="M21 12A9 9 0 0 0 6 5.3L3 8"></path>
                    <path d="M21 22v-6h-6"></path>
                    <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"></path>
                  </svg>
                </button>
              </div>
            </div>
            <div class="flex-grow overflow-hidden relative p-0">
              <div
                id="output"
                class="absolute inset-0 w-full h-full text-center text-[--text-secondary] flex items-center justify-center"
                style="cursor: grab"
              >
                <p id="placeholder-text">Blok-sxeminiz burada göstəriləcək.</p>
                <div id="error-message" class="text-red-400 hidden"></div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Modal -->
    <div
      id="details-modal"
      class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 hidden z-50"
    >
      <div
        class="bg-[#222] rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col"
      >
        <div
          class="p-4 border-b border-gray-600 flex justify-between items-center"
        >
          <h3 id="modal-title" class="text-xl font-bold">Addımın İzahı</h3>
          <button id="modal-close" class="text-gray-400 hover:text-white">
            &times;
          </button>
        </div>
        <div id="modal-content" class="p-6 overflow-y-auto text-gray-300">
          <!-- Detailed content will be injected here -->
        </div>
      </div>
    </div>

    <script type="module">
      import mermaid from "https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs";

      const generateBtn = document.getElementById("generateBtn");
      const userInput = document.getElementById("userInput");
      const langSelect = document.getElementById("lang-select");
      const outputDiv = document.getElementById("output");
      const loader = document.getElementById("loader");
      const btnText = document.getElementById("btn-text");
      const placeholderText = document.getElementById("placeholder-text");
      const errorMessage = document.getElementById("error-message");
      const chatSection = document.getElementById("chat-section");
      const chatMessages = document.getElementById("chat-messages");
      const chatInput = document.getElementById("chatInput");
      const sendBtn = document.getElementById("sendBtn");
      const sendBtnText = document.getElementById("send-btn-text");
      const chatLoader = document.getElementById("chat-loader");
      const modal = document.getElementById("details-modal");
      const modalTitle = document.getElementById("modal-title");
      const modalContent = document.getElementById("modal-content");
      const modalClose = document.getElementById("modal-close");
      const historyPanel = document.getElementById("history-panel");
      const newChatBtn = document.getElementById("new-chat-btn");
      const historyList = document.getElementById("history-list");

      let sessions = [];
      let activeSessionId = null;
      let mermaidContext = "";
      let conversationHistory = [];
      let detailsMap = new Map();
      let panZoomInstance = null;

      // Initialize pan-zoom functionality
      const initializePanZoom = () => {
        const svgElement = outputDiv.querySelector("svg");
        if (svgElement && !panZoomInstance) {
          try {
            // Hide placeholder text
            const placeholderText = document.getElementById("placeholder-text");
            if (placeholderText) {
              placeholderText.style.display = "none";
            }

            // Set SVG to fill container completely
            svgElement.style.position = "absolute";
            svgElement.style.top = "0";
            svgElement.style.left = "0";
            svgElement.style.width = "100%";
            svgElement.style.height = "100%";
            svgElement.setAttribute("preserveAspectRatio", "none");
            svgElement.removeAttribute("width");
            svgElement.removeAttribute("height");

            // Get SVG dimensions and adjust container height
            const svgViewBox = svgElement.getAttribute("viewBox");
            let svgHeight = 500; // default height

            if (svgViewBox) {
              const viewBoxValues = svgViewBox.split(" ");
              if (viewBoxValues.length === 4) {
                svgHeight = Math.max(parseInt(viewBoxValues[3]), 500);
              }
            }

            // Set minimum height for canvas container
            const canvasContainer = document.querySelector(
              ".flex-grow.p-6.pt-0 > div"
            );
            if (canvasContainer) {
              const minHeight = Math.max(svgHeight + 100, 600); // Add padding and minimum
              canvasContainer.style.minHeight = `${minHeight}px`;
            }

            panZoomInstance = svgPanZoom(svgElement, {
              zoomEnabled: true,
              controlIconsEnabled: false,
              fit: false,
              center: false,
              minZoom: 0.1,
              maxZoom: 10,
              zoomScaleSensitivity: 0.2,
              panEnabled: true,
              dblClickZoomEnabled: true,
              mouseWheelZoomEnabled: true,
              preventMouseEventsDefault: false,
              eventsListenerElement: svgElement,
              beforePan: function () {
                outputDiv.classList.add("panning");
              },
              onPan: function () {
                outputDiv.classList.remove("panning");
              },
            });

            // Show canvas controls
            document
              .getElementById("canvas-controls")
              .classList.remove("hidden");

            // Add click event listener for node details
            svgElement.addEventListener("click", (e) => {
              const clickedElement = e.target.closest("[data-id]");
              if (clickedElement) {
                const nodeId = clickedElement.getAttribute("data-id");
                showDetails(nodeId);
              }
            });
          } catch (error) {
            console.error("Pan-zoom initialization error:", error);
          }
        }
      };

      const createNewSession = () => {
        const newSession = {
          id: `session_${Date.now()}`,
          title: "Yeni Söhbət",
          userInput: "",
          lang: "JavaScript",
          mermaidContext: "",
          detailsMap: [],
          conversationHistory: [],
          createdAt: new Date().toISOString(),
        };
        sessions.unshift(newSession);
        activeSessionId = newSession.id;
        saveSessions();
        loadSession(activeSessionId);
        renderHistory();
      };

      const saveSessions = () => {
        localStorage.setItem("chatSessions", JSON.stringify(sessions));
        localStorage.setItem("activeSessionId", activeSessionId);
      };

      const loadSessions = () => {
        const savedSessions = localStorage.getItem("chatSessions");
        const savedActiveId = localStorage.getItem("activeSessionId");
        if (savedSessions) {
          sessions = JSON.parse(savedSessions);
          activeSessionId =
            savedActiveId || (sessions.length > 0 ? sessions[0].id : null);
        } else {
          createNewSession();
        }
      };

      const clearUI = () => {
        userInput.value = "";
        langSelect.value = "JavaScript";
        if (panZoomInstance) {
          panZoomInstance.destroy();
          panZoomInstance = null;
        }
        outputDiv.innerHTML = `<p id="placeholder-text">Blok-sxeminiz burada göstəriləcək.</p>`;
        errorMessage.classList.add("hidden");
        document.getElementById("canvas-controls").classList.add("hidden");
        chatSection.classList.add("hidden");
        chatMessages.innerHTML = "";
        mermaidContext = "";
        conversationHistory = [];
        detailsMap.clear();
      };

      const loadSession = (sessionId) => {
        const session = sessions.find((s) => s.id === sessionId);
        if (!session) {
          console.error(`Session with ID ${sessionId} not found.`);
          createNewSession();
          return;
        }

        activeSessionId = sessionId;
        clearUI();

        userInput.value = session.userInput;
        langSelect.value = session.lang;
        mermaidContext = session.mermaidContext;
        detailsMap = new Map(session.detailsMap);
        conversationHistory = session.conversationHistory;

        if (session.mermaidContext) {
          restoreState();
        }

        renderHistory();
        saveSessions();
      };

      const renderHistory = () => {
        historyList.innerHTML = "";
        sessions.forEach((session) => {
          const li = document.createElement("li");
          li.className = `p-2 rounded-md cursor-pointer hover:bg-gray-700 transition-colors duration-200 text-sm truncate ${
            session.id === activeSessionId ? "bg-gray-700 font-semibold" : ""
          }`;
          li.textContent = session.title;
          li.setAttribute("data-session-id", session.id);
          li.addEventListener("click", () => {
            loadSession(session.id);
          });
          historyList.appendChild(li);
        });
      };

      const updateCurrentSession = (data) => {
        const sessionIndex = sessions.findIndex(
          (s) => s.id === activeSessionId
        );
        if (sessionIndex !== -1) {
          sessions[sessionIndex] = { ...sessions[sessionIndex], ...data };
          if (
            data.userInput &&
            sessions[sessionIndex].title === "Yeni Söhbət"
          ) {
            sessions[sessionIndex].title =
              data.userInput.substring(0, 30) +
              (data.userInput.length > 30 ? "..." : "");
          }
          saveSessions();
          renderHistory();
        }
      };

      document.addEventListener("DOMContentLoaded", () => {
        loadSessions();
        if (sessions.length === 0) {
          createNewSession();
        } else {
          loadSession(activeSessionId);
        }
      });

      newChatBtn.addEventListener("click", createNewSession);

      userInput.addEventListener("input", () => {
        updateCurrentSession({ userInput: userInput.value });
      });

      langSelect.addEventListener("change", () => {
        updateCurrentSession({ lang: langSelect.value });
      });

      const restoreState = async () => {
        if (!mermaidContext) return;

        try {
          placeholderText.classList.add("hidden");
          errorMessage.classList.add("hidden");

          mermaid.initialize({
            startOnLoad: false,
            securityLevel: "loose",
            theme: "base",
            themeVariables: {
              background: "#111111",
              primaryColor: "#222222",
              primaryTextColor: "#ffffff",
              lineColor: "#666666",
              textColor: "#ffffff",
            },
          });

          const { svg, bindFunctions } = await mermaid.render(
            "graphDiv",
            mermaidContext
          );
          outputDiv.innerHTML = svg;
          if (bindFunctions) {
            bindFunctions(outputDiv);
          }

          detailsMap.forEach((value, key) => {
            const nodeElements = outputDiv.querySelectorAll(
              `#${CSS.escape(key)}, .node[id$="-${key}"]`
            );
            nodeElements.forEach((nodeElement) => {
              if (nodeElement) {
                nodeElement.setAttribute("data-id", key);
              }
            });
          });

          // Initialize pan-zoom functionality
          initializePanZoom();

          chatMessages.innerHTML = "";
          conversationHistory.forEach((msg) => {
            addMessageToChat(
              msg.parts[0].text,
              msg.role === "user" ? "user" : "assistant"
            );
          });
          chatSection.classList.remove("hidden");
        } catch (error) {
          console.error("Error restoring state:", error);
          errorMessage.textContent =
            "Yadda saxlanılmış vəziyyəti bərpa etmək mümkün olmadı.";
          errorMessage.classList.remove("hidden");
        }
      };

      window.showDetails = (nodeId) => {
        if (detailsMap.has(nodeId)) {
          const detailText = detailsMap.get(nodeId);
          const nodeElement = document.querySelector(`[data-id="${nodeId}"]`);
          const title = nodeElement
            ? nodeElement.textContent || "Addımın İzahı"
            : "Addımın İzahı";

          modalTitle.textContent = title;
          modalContent.innerHTML = marked.parse(
            detailText
              .replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
                return `\n\`\`\`${lang || ""}\n${code.trim()}\n\`\`\``;
              })
              .replace(/\n/g, "\n\n")
          );

          modal.classList.remove("hidden");
        } else {
          console.warn(`No details found for node ID: ${nodeId}`);
          modalTitle.textContent = "Məlumat Tapılmadı";
          modalContent.innerHTML = `<p>Bu blok üçün ətraflı məlumat tapılmadı.</p>`;
          modal.classList.remove("hidden");
        }
      };

      const generateFlowchart = async () => {
        const prompt = userInput.value.trim();
        const selectedLang = langSelect.value;
        if (!prompt) {
          errorMessage.textContent = "Zəhmət olmasa, bir təsvir daxil edin.";
          errorMessage.classList.remove("hidden");
          placeholderText.classList.add("hidden");
          return;
        }

        loader.classList.remove("hidden");
        btnText.textContent = "Generasiya edilir...";
        generateBtn.disabled = true;
        outputDiv.innerHTML = "";
        errorMessage.classList.add("hidden");
        placeholderText.classList.add("hidden");
        chatSection.classList.add("hidden");

        const systemPrompt = `
                Sən, yeni başlayanlar üçün proqramlaşdırma müəllimisən. Vəzifən, istifadəçi tələbini analiz edərək, seçilmiş proqramlaşdırma dilində ultra-detallı, interaktiv blok-sxem yaratmaqdır.
                SEÇİLMİŞ PROQRAMLAŞDIRMA DİLİ: ${selectedLang}

                ƏSAS TƏLİMATLAR:
                1.  İKİLİ FORMATDA ÇIXIŞ: Hər bir addım üçün iki hissə yaratmalısan: qısa başliq və detallı izah.
                2.  QISA BAŞLIQ: Blok-sxemin özündə görünəcək çox qısa, bir neçə sözlük mətn.
                3.  DETALLI İZAH: İstifadəçi bloka klikləyəndə popup-da görünəcək ətraflı izah. Bu izah addımın nə üçün lazım olduğunu, texniki detallarını və seçilmiş dildə kod nümunəsini ehtiva etməlidir. Kodu \`\`\`${selectedLang.toLowerCase()}__NL__...__NL__\`\`\` bloku içinə al.
                4.  XÜSUSİ FORMAT (ÇOX VACİB): Hər bir bloku bu formatda yarat: NodeID["Qısa Başlıq"]. Sonra, ayrıca olaraq, komment şəklində detallı izahı əlavə et: %% NodeID:::DETALLI İZAH BURADA. Mütləq ':::' separatordan istifadə et. Yeni sətirlər üçün real yeni sətir (\n) yerinə __NL__ xüsusi mətnindən istifadə et. Kommentlər (%%) MÜTLƏQ öz ayrıca sətrində olmalıdır.
                5.  İNTERAKTİVLİK: Hər node üçün 'click NodeID call showDetails("NodeID")' direktivini əlavə et. Bu, MÜTLƏQ hər bir node üçün edilməlidir.
                6.  MİNİMUM BLOK SAYI: Yaranan qrafik minimum 50 blokdan ibarət olmalıdır.
                7.  SADƏCƏ MERMAID KODU ÇIXAR: Çıxışda əlavə mətn və ya \`\`\`mermaid bloku olmasın. Yalnız 'graph TD;' ilə başlayan saf Mermaid kodu olsun.

                NÜMUNƏ ÇIXIŞ:
                graph TD;
                %% A1:::Bu addım proqramın başlanğıc nöqtəsini bildirir. Heç bir kod tələb olunmur.
                %% B1:::Bu addımda istifadəçidən məlumat almaq üçün lazım olan 'mysql.connector' kitabxanasını idxal edirik.__NL__\`\`\`python__NL__import mysql.connector__NL__\`\`\`
                A1(("Başlanğıc")) --> B1["Kitabxananı İdxal Et"];
                click A1 call showDetails("A1")
                click B1 call showDetails("B1")
            `;

        try {
          let rawMermaidCode = await callGemini(systemPrompt, [
            {
              role: "user",
              parts: [{ text: `İstifadəçi təsviri budur: "${prompt}"` }],
            },
          ]);

          rawMermaidCode = rawMermaidCode
            .replace(/^```mermaid\s*/, "")
            .replace(/```\s*$/, "")
            .trim();

          detailsMap.clear();
          const lines = rawMermaidCode.split("\n");
          let graphTdFound = false;

          const processedLines = lines
            .map((line) => {
              const trimmedLine = line.trim();
              if (trimmedLine.startsWith("graph TD;")) {
                if (!graphTdFound) {
                  graphTdFound = true;
                  return "graph TD;";
                }
                return null;
              }
              if (trimmedLine.startsWith("%%")) {
                const parts = line.replace("%%", "").split(":::");
                if (parts.length > 1) {
                  const id = parts[0].trim();
                  const detail = parts
                    .slice(1)
                    .join(":::")
                    .replace(/__NL__/g, "\n");
                  detailsMap.set(id, detail);
                }
                return null;
              }
              const commentIndex = line.indexOf("%%");
              return commentIndex > -1
                ? line.substring(0, commentIndex).trim()
                : line;
            })
            .filter((line) => line !== null && line.trim() !== "");

          let cleanMermaidCode = processedLines.join("\n");
          if (!cleanMermaidCode.trim().startsWith("graph TD;")) {
            cleanMermaidCode = "graph TD;\n" + cleanMermaidCode;
          }

          mermaidContext = cleanMermaidCode;

          mermaid.initialize({
            startOnLoad: false,
            securityLevel: "loose",
            theme: "base",
            themeVariables: {
              background: "#111111",
              primaryColor: "#222222",
              primaryTextColor: "#ffffff",
              lineColor: "#666666",
              textColor: "#ffffff",
            },
          });

          const { svg, bindFunctions } = await mermaid.render(
            "graphDiv",
            mermaidContext
          );
          outputDiv.innerHTML = svg;
          if (bindFunctions) {
            bindFunctions(outputDiv);
          }

          detailsMap.forEach((value, key) => {
            const nodeElements = outputDiv.querySelectorAll(
              `#${CSS.escape(key)}, .node[id$="-${key}"]`
            );
            nodeElements.forEach((nodeElement) => {
              if (nodeElement) {
                nodeElement.setAttribute("data-id", key);
              }
            });
          });

          // Initialize pan-zoom functionality
          initializePanZoom();

          conversationHistory = [];
          chatMessages.innerHTML = "";
          addMessageToChat(
            `Blok-sxeminiz ${selectedLang} dilində hazırlandı. Daha çox məlumat üçün bloklara klikləyin və ya aşağıda sual verin.`,
            "assistant"
          );
          chatSection.classList.remove("hidden");

          // Save state to current session
          updateCurrentSession({
            mermaidContext: mermaidContext,
            detailsMap: Array.from(detailsMap.entries()),
            conversationHistory: conversationHistory,
            userInput: prompt,
          });
        } catch (error) {
          console.error("Flowchart Error:", error);
          errorMessage.textContent = error.message;
          errorMessage.classList.remove("hidden");
          placeholderText.classList.remove("hidden");
        } finally {
          loader.classList.add("hidden");
          btnText.textContent = "Generasiya et";
          generateBtn.disabled = false;
        }
      };

      const handleChatMessage = async () => {
        const userQuestion = chatInput.value.trim();
        if (!userQuestion || sendBtn.disabled) return;
        addMessageToChat(userQuestion, "user");
        conversationHistory.push({
          role: "user",
          parts: [{ text: userQuestion }],
        });
        chatInput.value = "";
        chatLoader.classList.remove("hidden");
        sendBtnText.textContent = "";
        sendBtn.disabled = true;

        const chatPrompt = `Sən, yeni başlayanlar üçün proqramlaşdırma müəllimisən. Vəzifən, aşağıda verilmiş blok-sxem konteksti haqqında istifadəçinin suallarını cavablandırmaqdır. İstifadəçi heç nə bilmir.
                VACİB QAYDALAR:
                1. Cavablarını formatlamaq üçün Markdown istifadə et. Məsələn, siyahılar üçün nömrəli və ya işarəli siyahılar, vacib sözlər üçün **qalın** yazı, kod nümunələri üçün isə \`inline kod\` və ya kod blokları (\`\`\`) istifadə et.
                2. Cavabların ultra-detallı və addım-addım olmalıdır. Ümumi danışma.
                3. Bir kod parçasını izah edərkən, hər bir sətrin, dəyişənin və funksiyanın mənasını açıqla.
                4. YALNIZ verilmiş blok-sxem və onunla əlaqəli proqramlaşdırma mövzuları haqqında danış. Kənar suallara cavab verməkdən imtina et və istifadəçini nəzakətlə mövzuya qaytar.
                
                Blok-sxem konteksti (istifadəçi görmür, yalnız sən kontekst üçün istifadə edirsən): \`\`\`mermaid\n${mermaidContext}\n\`\`\`.
                Detallar (istifadəçi görmür): ${JSON.stringify(
                  Array.from(detailsMap.entries())
                )}`;

        try {
          const assistantResponse = await callGemini(
            chatPrompt,
            conversationHistory
          );
          addMessageToChat(assistantResponse, "assistant");
          conversationHistory.push({
            role: "model",
            parts: [{ text: assistantResponse }],
          });
          // Save conversation history to current session
          updateCurrentSession({ conversationHistory: conversationHistory });
        } catch (error) {
          addMessageToChat(error.message, "assistant");
        } finally {
          chatLoader.classList.add("hidden");
          sendBtnText.textContent = "Göndər";
          sendBtn.disabled = false;
          chatInput.focus();
        }
      };

      const callGemini = async (systemText, history = []) => {
        let contents = [
          ...history,
          { role: "user", parts: [{ text: systemText }] },
        ];
        const payload = { contents };
        const apiKey = "AIzaSyCgMgqq4PmHB8HND-tuqXDvPS9zQz62Gqk";
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const errorBody = await response.text();
          let errorMessage = `API Xətası: ${response.status} ${response.statusText}.`;
          if (response.status === 401) {
            errorMessage =
              "API Xətası: 401 (İcazə Yoxdur). Bu, proqramın Gemini API ilə əlaqə qurmaq üçün icazəsi olmadığını göstərir. Təqdim edilən API açarı etibarsız və ya səhv ola bilər.";
          }
          if (errorBody) {
            errorMessage += ` Server cavabı: ${errorBody}`;
          }
          throw new Error(errorMessage);
        }

        const result = await response.json();
        if (
          result.candidates &&
          result.candidates[0].content &&
          result.candidates[0].content.parts
        ) {
          return result.candidates[0].content.parts[0].text;
        } else {
          let errorText =
            "API cavabında gözlənilməz format və ya məzmun bloklanıb.";
          if (result.promptFeedback && result.promptFeedback.blockReason) {
            errorText = `Sorğu bloklandı. Səbəb: ${result.promptFeedback.blockReason}`;
          }
          throw new Error(errorText);
        }
      };

      const addMessageToChat = (text, sender) => {
        const messageDiv = document.createElement("div");
        messageDiv.classList.add(
          "chat-message",
          "p-3",
          "rounded-lg",
          "w-full",
          sender === "user" ? "user-message" : "assistant-message"
        );
        if (sender === "assistant") {
          messageDiv.innerHTML = marked.parse(text);
        } else {
          messageDiv.textContent = text;
        }
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
      };

      // Event Listeners
      generateBtn.addEventListener("click", generateFlowchart);
      userInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          generateFlowchart();
        }
      });
      sendBtn.addEventListener("click", handleChatMessage);
      chatInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          handleChatMessage();
        }
      });
      modalClose.addEventListener("click", () => modal.classList.add("hidden"));
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.add("hidden");
        }
      });

      // Canvas control event listeners
      document.getElementById("zoom-in").addEventListener("click", () => {
        if (panZoomInstance) {
          panZoomInstance.zoomIn();
        }
      });

      document.getElementById("zoom-out").addEventListener("click", () => {
        if (panZoomInstance) {
          panZoomInstance.zoomOut();
        }
      });

      document.getElementById("reset-pan").addEventListener("click", () => {
        if (panZoomInstance) {
          panZoomInstance.resetZoom();
          panZoomInstance.center();
          panZoomInstance.fit();
        }
      });
    </script>
  </body>
</html>
